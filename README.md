Sanal ortam başlatma komutu:
venv\Scripts\activate

sanal ortam kapatma komutu:
deactivate

DroneProject/
├── AirSim/                # AirSim'un resmi kaynak kodları (git clone ile alınır)
├── ardupilot/             # ArduPilot'un resmi kaynak kodları (git clone ile alınır)
├── docs/                  # Proje belgeleri, notlar
├── logs/                  # Simülasyon ya da uçuş günlükleri (tlog, bin vs.)
├── missions/              # Görev planları (waypoint dosyaları, .waypoints)
├── models/                # Simülasyon modelleri (örneğin Gazebo, SITL özel araçlar)
├── scripts/               # Python betikleri, kontrol kodları
├── requirements.txt       # Proje ana bağımlılıkları
├── requirements-dev.txt   # Geliştirici bağımlılıkları
├── setup_env.ps1          # Ortam kurulum scripti (PowerShell)
└── README.md              # Proje hakkında genel bil<PERSON> / Dosya	Ne için kullanılır?

AirSim/     AirSim'un resmi kaynak kodları (git clone ile alınır)
ardupilot/  ArduPilot’un resmi kaynak kodları (git ile klonlanır)
docs/	Kurulum notları, kullanım belgeleri
logs/	Telemetri günlükleri (uçuş verisi analizi için)
missions/	Mission Planner veya QGroundControl'den alınan görev dosyaları (.waypoint)
models/	Simülasyon ortamları, özel dronelara ait .xml veya .xacro dosyaları
scripts/	Drone'u kontrol eden kendi yazdığın Python dosyaları
requirements.txt	Proje ana bağımlılıkları
requirements-dev.txt	Geliştirici bağımlılıkları
setup_env.ps1	Ortam kurulum scripti (PowerShell)
README.md	Projenin açıklaması, çalışma talimatları
venv/	Proje bağımlılıklarını izole eden Python sanal ortam klasörü

